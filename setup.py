#!/usr/bin/env python3
"""
Setup script for Turn Me On Android game
This script helps set up the development environment and build the Android APK
"""

import os
import sys
import subprocess
import platform

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n{description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✓ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {description} failed:")
        print(f"Error: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("✗ Python 3.8 or higher is required")
        return False
    print(f"✓ Python {version.major}.{version.minor}.{version.micro} is compatible")
    return True

def install_dependencies():
    """Install required Python packages"""
    print("\nInstalling Python dependencies...")
    
    # Install basic requirements
    packages = [
        "kivy>=2.1.0",
        "kivymd>=1.1.1", 
        "buildozer>=1.4.0",
        "cython>=0.29.0"
    ]
    
    for package in packages:
        if not run_command(f"pip install {package}", f"Installing {package}"):
            return False
    
    return True

def setup_android_environment():
    """Set up Android development environment"""
    print("\nSetting up Android environment...")
    
    # Check if Java is installed
    if not run_command("java -version", "Checking Java installation"):
        print("Please install Java JDK 8 or higher")
        return False
    
    # Initialize buildozer
    if not os.path.exists(".buildozer"):
        run_command("buildozer init", "Initializing buildozer")
    
    return True

def build_debug_apk():
    """Build debug APK"""
    print("\nBuilding debug APK...")
    return run_command("buildozer android debug", "Building debug APK")

def build_release_apk():
    """Build release APK"""
    print("\nBuilding release APK...")
    return run_command("buildozer android release", "Building release APK")

def test_desktop():
    """Test the game on desktop"""
    print("\nTesting game on desktop...")
    return run_command("python main.py", "Running desktop test")

def main():
    """Main setup function"""
    print("Turn Me On - Android Game Setup")
    print("=" * 40)
    
    if not check_python_version():
        sys.exit(1)
    
    # Change to script directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    print(f"\nWorking directory: {os.getcwd()}")
    
    # Menu options
    while True:
        print("\nSetup Options:")
        print("1. Install dependencies")
        print("2. Setup Android environment")
        print("3. Test on desktop")
        print("4. Build debug APK")
        print("5. Build release APK")
        print("6. Full setup (1 + 2)")
        print("7. Exit")
        
        choice = input("\nEnter your choice (1-7): ").strip()
        
        if choice == "1":
            install_dependencies()
        elif choice == "2":
            setup_android_environment()
        elif choice == "3":
            test_desktop()
        elif choice == "4":
            build_debug_apk()
        elif choice == "5":
            build_release_apk()
        elif choice == "6":
            if install_dependencies() and setup_android_environment():
                print("\n✓ Full setup completed successfully!")
            else:
                print("\n✗ Setup failed. Please check the errors above.")
        elif choice == "7":
            print("Goodbye!")
            break
        else:
            print("Invalid choice. Please enter 1-7.")

if __name__ == "__main__":
    main()
