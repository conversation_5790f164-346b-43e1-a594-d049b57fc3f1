#!/usr/bin/env python3
"""
Test script for Turn Me On Android game
This script tests basic functionality without requiring <PERSON><PERSON> to be installed
"""

import sys
import os

def test_imports():
    """Test if all required modules can be imported"""
    print("Testing imports...")
    
    try:
        # Test standard library imports
        import random
        import math
        import json
        print("✓ Standard library imports successful")
        
        # Test if files exist
        required_files = [
            'main.py',
            'ui.py', 
            'buildozer.spec',
            'requirements.txt',
            'assets/bulb_off.png',
            'assets/bulb_on.png',
            'assets/star.png',
            'assets/gremlin.png'
        ]
        
        missing_files = []
        for file in required_files:
            if not os.path.exists(file):
                missing_files.append(file)
        
        if missing_files:
            print(f"✗ Missing files: {missing_files}")
            return False
        else:
            print("✓ All required files present")
        
        return True
        
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False

def test_game_constants():
    """Test game constants and configuration"""
    print("\nTesting game constants...")
    
    try:
        # Import constants from main.py without running the app
        import importlib.util
        spec = importlib.util.spec_from_file_location("main", "main.py")
        main_module = importlib.util.module_from_spec(spec)
        
        # Check if constants are defined (this will fail if Kivy isn't installed)
        # But we can at least check the file structure
        with open('main.py', 'r') as f:
            content = f.read()
            
        required_constants = [
            'SCREEN_WIDTH',
            'SCREEN_HEIGHT', 
            'FPS',
            'BRIGHTNESS_DECAY',
            'BULB_ON_VALUE'
        ]
        
        missing_constants = []
        for constant in required_constants:
            if constant not in content:
                missing_constants.append(constant)
        
        if missing_constants:
            print(f"✗ Missing constants: {missing_constants}")
            return False
        else:
            print("✓ All required constants found")
            
        return True
        
    except Exception as e:
        print(f"✗ Error testing constants: {e}")
        return False

def test_buildozer_config():
    """Test buildozer configuration"""
    print("\nTesting buildozer configuration...")
    
    try:
        with open('buildozer.spec', 'r') as f:
            config = f.read()
        
        required_settings = [
            'title = Turn Me On',
            'package.name = turnmeon',
            'requirements = python3,kivy,kivymd',
            'orientation = portrait'
        ]
        
        missing_settings = []
        for setting in required_settings:
            if setting not in config:
                missing_settings.append(setting)
        
        if missing_settings:
            print(f"✗ Missing buildozer settings: {missing_settings}")
            return False
        else:
            print("✓ Buildozer configuration looks good")
            
        return True
        
    except Exception as e:
        print(f"✗ Error testing buildozer config: {e}")
        return False

def test_asset_files():
    """Test that asset files exist and are readable"""
    print("\nTesting asset files...")
    
    asset_files = [
        'assets/bulb_off.png',
        'assets/bulb_on.png', 
        'assets/star.png',
        'assets/gremlin.png',
        'assets/bulb_click.wav',
        'assets/bulb_break.wav',
        'assets/gremlin_hit.wav',
        'assets/star_collect.wav'
    ]
    
    missing_assets = []
    for asset in asset_files:
        if not os.path.exists(asset):
            missing_assets.append(asset)
        else:
            # Check if file is readable and not empty
            try:
                with open(asset, 'rb') as f:
                    data = f.read(100)  # Read first 100 bytes
                    if len(data) == 0:
                        missing_assets.append(f"{asset} (empty)")
            except Exception:
                missing_assets.append(f"{asset} (unreadable)")
    
    if missing_assets:
        print(f"✗ Missing or problematic assets: {missing_assets}")
        return False
    else:
        print("✓ All asset files present and readable")
        return True

def main():
    """Run all tests"""
    print("Turn Me On - Android Game Test Suite")
    print("=" * 40)
    
    # Change to script directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    tests = [
        test_imports,
        test_game_constants,
        test_buildozer_config,
        test_asset_files
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 40)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All tests passed! The game should be ready for building.")
        print("\nNext steps:")
        print("1. Install Kivy: pip install kivy")
        print("2. Test on desktop: python main.py")
        print("3. Build for Android: python setup.py")
    else:
        print("✗ Some tests failed. Please fix the issues above.")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
