"""
UI components for the Turn Me On Android game
"""

from kivy.uix.label import Label
from kivy.uix.button import Button
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.floatlayout import FloatLayout
from kivy.uix.progressbar import ProgressBar
from kivy.uix.popup import Popup
from kivy.graphics import Color, Rectangle
from kivy.metrics import dp
from kivy.animation import Animation

# Colors for UI elements
UI_COLORS = {
    'background': (0.1, 0.1, 0.1, 0.8),
    'text': (1, 1, 1, 1),
    'score': (1, 1, 0, 1),  # Yellow
    'brightness_high': (0, 1, 0, 1),  # Green
    'brightness_medium': (1, 1, 0, 1),  # Yellow
    'brightness_low': (1, 0, 0, 1),  # Red
    'button': (0.2, 0.2, 0.2, 1),
    'button_pressed': (0.4, 0.4, 0.4, 1)
}


class GameUI(FloatLayout):
    """Main UI overlay for the game"""
    
    def __init__(self, game_app, **kwargs):
        super().__init__(**kwargs)
        self.game_app = game_app
        self.setup_ui()
    
    def setup_ui(self):
        """Set up the UI elements"""
        # Top bar for score and stats
        self.top_bar = BoxLayout(
            orientation='horizontal',
            size_hint=(1, None),
            height=dp(60),
            pos_hint={'top': 1},
            spacing=dp(10),
            padding=[dp(10), dp(10), dp(10), dp(10)]
        )
        
        # Add semi-transparent background to top bar
        with self.top_bar.canvas.before:
            Color(*UI_COLORS['background'])
            self.top_bar_bg = Rectangle(size=self.top_bar.size, pos=self.top_bar.pos)
        
        self.top_bar.bind(size=self.update_top_bar_bg, pos=self.update_top_bar_bg)
        
        # Score label
        self.score_label = Label(
            text='Score: 0',
            color=UI_COLORS['score'],
            font_size=dp(18),
            size_hint=(None, 1),
            width=dp(100)
        )
        
        # High score label
        self.high_score_label = Label(
            text='Best: 0',
            color=UI_COLORS['text'],
            font_size=dp(14),
            size_hint=(None, 1),
            width=dp(80)
        )
        
        # Brightness bar
        self.brightness_bar = ProgressBar(
            max=100,
            value=100,
            size_hint=(0.3, 0.6),
            pos_hint={'center_y': 0.5}
        )
        
        # Brightness percentage label
        self.brightness_label = Label(
            text='100%',
            color=UI_COLORS['text'],
            font_size=dp(14),
            size_hint=(None, 1),
            width=dp(50)
        )
        
        # Combo label (only visible when combo > 0)
        self.combo_label = Label(
            text='',
            color=(1, 0.65, 0, 1),  # Orange
            font_size=dp(16),
            size_hint=(None, 1),
            width=dp(80)
        )
        
        # Overcharge indicator
        self.overcharge_label = Label(
            text='',
            color=UI_COLORS['score'],
            font_size=dp(14),
            size_hint=(None, 1),
            width=dp(80)
        )
        
        # Add elements to top bar
        self.top_bar.add_widget(self.score_label)
        self.top_bar.add_widget(self.high_score_label)
        self.top_bar.add_widget(self.brightness_bar)
        self.top_bar.add_widget(self.brightness_label)
        self.top_bar.add_widget(self.combo_label)
        self.top_bar.add_widget(self.overcharge_label)
        
        # Pause button
        self.pause_button = Button(
            text='⏸',
            size_hint=(None, None),
            size=(dp(50), dp(50)),
            pos_hint={'right': 0.95, 'top': 0.95},
            font_size=dp(20)
        )
        self.pause_button.bind(on_press=self.toggle_pause)
        
        # Add UI elements to layout
        self.add_widget(self.top_bar)
        self.add_widget(self.pause_button)
    
    def update_top_bar_bg(self, *args):
        """Update top bar background when size/position changes"""
        self.top_bar_bg.size = self.top_bar.size
        self.top_bar_bg.pos = self.top_bar.pos
    
    def update_display(self):
        """Update all UI elements with current game state"""
        # Update score
        self.score_label.text = f'Score: {self.game_app.score}'
        
        # Update high score
        high_score = self.game_app.high_scores[self.game_app.difficulty_level]
        self.high_score_label.text = f'Best: {high_score}'
        
        # Update brightness bar and label
        brightness = max(0, min(100, self.game_app.brightness))
        self.brightness_bar.value = brightness
        self.brightness_label.text = f'{int(brightness)}%'
        
        # Update brightness bar color based on level
        if brightness > 60:
            bar_color = UI_COLORS['brightness_high']
        elif brightness > 30:
            bar_color = UI_COLORS['brightness_medium']
        else:
            bar_color = UI_COLORS['brightness_low']
        
        # Update combo display
        if self.game_app.combo_count > 0:
            self.combo_label.text = f'Combo: {self.game_app.combo_count}x'
        else:
            self.combo_label.text = ''
        
        # Update overcharge display
        if self.game_app.is_overcharged():
            multiplier = self.game_app.get_overcharge_multiplier()
            self.overcharge_label.text = f'⚡{self.game_app.overcharge_level} ({multiplier}x)'
        else:
            self.overcharge_label.text = ''
    
    def toggle_pause(self, button):
        """Toggle game pause state"""
        self.game_app.paused = not self.game_app.paused
        if self.game_app.paused:
            button.text = '▶'
            self.show_pause_overlay()
        else:
            button.text = '⏸'
            self.hide_pause_overlay()
    
    def show_pause_overlay(self):
        """Show pause overlay"""
        if hasattr(self, 'pause_overlay'):
            return
        
        self.pause_overlay = FloatLayout()
        
        # Semi-transparent background
        with self.pause_overlay.canvas:
            Color(0, 0, 0, 0.7)
            self.pause_bg = Rectangle(size=self.size, pos=self.pos)
        
        # Pause text
        pause_label = Label(
            text='PAUSED',
            font_size=dp(48),
            color=UI_COLORS['text'],
            pos_hint={'center_x': 0.5, 'center_y': 0.6}
        )
        
        # Resume instructions
        resume_label = Label(
            text='Tap pause button to resume',
            font_size=dp(18),
            color=UI_COLORS['text'],
            pos_hint={'center_x': 0.5, 'center_y': 0.4}
        )
        
        self.pause_overlay.add_widget(pause_label)
        self.pause_overlay.add_widget(resume_label)
        self.add_widget(self.pause_overlay)
        
        self.pause_overlay.bind(size=self.update_pause_bg, pos=self.update_pause_bg)
    
    def update_pause_bg(self, *args):
        """Update pause overlay background"""
        if hasattr(self, 'pause_bg'):
            self.pause_bg.size = self.pause_overlay.size
            self.pause_bg.pos = self.pause_overlay.pos
    
    def hide_pause_overlay(self):
        """Hide pause overlay"""
        if hasattr(self, 'pause_overlay'):
            self.remove_widget(self.pause_overlay)
            del self.pause_overlay


class GameOverPopup(Popup):
    """Game over popup with restart option"""
    
    def __init__(self, game_app, **kwargs):
        self.game_app = game_app
        
        # Create content layout
        content = BoxLayout(orientation='vertical', spacing=dp(20), padding=dp(20))
        
        # Game over title
        title_label = Label(
            text='GAME OVER',
            font_size=dp(36),
            color=(1, 0, 0, 1),
            size_hint_y=None,
            height=dp(50)
        )
        
        # Final score
        score_label = Label(
            text=f'Final Score: {game_app.score}',
            font_size=dp(24),
            color=UI_COLORS['score'],
            size_hint_y=None,
            height=dp(40)
        )
        
        # Survival time
        time_label = Label(
            text=f'Survival Time: {int(game_app.game_time)}s',
            font_size=dp(18),
            color=UI_COLORS['text'],
            size_hint_y=None,
            height=dp(30)
        )
        
        # High score info
        high_score = game_app.high_scores[game_app.difficulty_level]
        if game_app.score == high_score and game_app.score > 0:
            high_score_text = 'NEW HIGH SCORE!'
            high_score_color = UI_COLORS['score']
        else:
            high_score_text = f'High Score: {high_score}'
            high_score_color = UI_COLORS['text']
        
        high_score_label = Label(
            text=high_score_text,
            font_size=dp(18),
            color=high_score_color,
            size_hint_y=None,
            height=dp(30)
        )
        
        # Restart button
        restart_button = Button(
            text='Restart Game',
            font_size=dp(20),
            size_hint_y=None,
            height=dp(50)
        )
        restart_button.bind(on_press=self.restart_game)
        
        # Add elements to content
        content.add_widget(title_label)
        content.add_widget(score_label)
        content.add_widget(time_label)
        content.add_widget(high_score_label)
        content.add_widget(restart_button)
        
        super().__init__(
            title='',
            content=content,
            size_hint=(0.8, 0.6),
            auto_dismiss=False,
            **kwargs
        )
    
    def restart_game(self, button):
        """Restart the game"""
        self.dismiss()
        self.game_app.restart_game()


class DifficultySelectionPopup(Popup):
    """Difficulty selection popup"""
    
    def __init__(self, game_app, **kwargs):
        self.game_app = game_app
        
        # Create content layout
        content = BoxLayout(orientation='vertical', spacing=dp(15), padding=dp(20))
        
        # Title
        title_label = Label(
            text='Choose Your Difficulty',
            font_size=dp(28),
            color=UI_COLORS['score'],
            size_hint_y=None,
            height=dp(50)
        )
        
        # Difficulty buttons
        easy_button = Button(
            text='Easy\nFewer gremlins, slower pace',
            font_size=dp(16),
            size_hint_y=None,
            height=dp(80)
        )
        easy_button.bind(on_press=lambda x: self.select_difficulty('easy'))
        
        okay_button = Button(
            text='Okay\nBalanced challenge',
            font_size=dp(16),
            size_hint_y=None,
            height=dp(80)
        )
        okay_button.bind(on_press=lambda x: self.select_difficulty('okay'))
        
        madman_button = Button(
            text='Mad Man\nINSANE gremlin chaos!',
            font_size=dp(16),
            size_hint_y=None,
            height=dp(80)
        )
        madman_button.bind(on_press=lambda x: self.select_difficulty('mad_man'))
        
        # Add elements to content
        content.add_widget(title_label)
        content.add_widget(easy_button)
        content.add_widget(okay_button)
        content.add_widget(madman_button)
        
        super().__init__(
            title='',
            content=content,
            size_hint=(0.9, 0.7),
            auto_dismiss=False,
            **kwargs
        )
    
    def select_difficulty(self, difficulty):
        """Select difficulty and start game"""
        self.game_app.difficulty_level = difficulty
        self.game_app.apply_difficulty_settings()
        self.dismiss()
        self.game_app.start_game()
