#:import md_icons kivymd.icon_definitions.md_icons


<MDLabel>
    disabled_color: self.theme_cls.disabled_hint_text_color
    text_size:
        (self.width if not self.adaptive_width else None) \
        if not self.adaptive_size else None, \
        None


<MDIcon>:
    canvas:
        Color:
            rgba: (1, 1, 1, 1) if self.source else (0, 0, 0, 0)
        Rectangle:
            group: "rectangle"
            source: self.source if self.source else None
            pos:
                self.pos \
                if not self.source else \
                (self.x - self._size[0] / 2, self.y)
            size:
                self._size \
                if self.source else \
                self.size

    font_style: "Icon"
    text: u"{}".format(md_icons[root.icon]) if root.icon in md_icons else "blank"
    source: None if root.icon in md_icons else root.icon

    # Badge icon.
    MDLabel:
        id: badge
        font_style: "Icon"
        adaptive_size: True
        opposite_icon_color: True
        color: root.badge_icon_color
        text:
            u"{}".format(md_icons[root.badge_icon]) \
            if root.badge_icon in md_icons else \
            ""
        pos:
            root.x + root.width / 2 + self.width / 2 - dp(6), \
            root.y + self.texture_size[1] / 2 + dp(6)
        font_size:
            ( \
            root.font_size / 1.5 \
            if not root.badge_font_size else \
            root.badge_font_size \
            ) \
            if root.badge_icon and root.badge_icon != "blank" else 0

        canvas.before:
            Color:
                rgba:
                    ( \
                    root.badge_bg_color \
                    if root.badge_bg_color else \
                    app.theme_cls.error_color \
                    ) \
                    if root.badge_icon else \
                    (0, 0, 0, 0)
            RoundedRectangle:
                group: "badge"
                radius: [self.width / 2,]
                pos: self.pos
                size: self.size
