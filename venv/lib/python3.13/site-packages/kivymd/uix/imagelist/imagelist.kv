<MDSmartTile>

    SmartTileImage:
        id: image
        mipmap: root.mipmap
        source: root.source
        radius: root.radius if root.radius else [0, 0, 0, 0]
        size_hint_y: 1 if root.overlap else None
        height: root.height if root.overlap else root.height - box.height
        pos:
            ((0, 0) if root.overlap else (0, box.height)) \
            if root.box_position == "footer" else \
            (0, 0)
        on_release: root.dispatch("on_release")
        on_press: root.dispatch("on_press")
        _no_ripple_effect: root._no_ripple_effect

    SmartTileOverlayBox:
        id: box
        md_bg_color: root.box_color
        size_hint_y: None
        padding: "8dp"
        radius: root.box_radius
        height: "68dp" if root.lines == 2 else "48dp"
        pos:
            (0, 0) \
            if root.box_position == "footer" else \
            (0, root.height - self.height)
