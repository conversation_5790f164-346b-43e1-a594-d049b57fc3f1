<TimeInputLabel@MDLabel>:
    theme_text_color: "Custom"
    font_size: dp(10)
    halign: "left"
    valign: "bottom"
    adaptive_size: True


<AmPmSelectorLabel>
    halign: "center"
    valign: "center"
    theme_text_color: "Custom"


<AmPmSelector>
    size_hint: None, None

    canvas.before:
        Color:
            rgba: root.border_color
        RoundedRectangle:
            pos: self.pos
            size: self.size
            radius: [root.border_radius, ]

        #AM
        Color:
            rgba: root._am_bg_color
        RoundedRectangle:
            pos:
                [ \
                self.pos[0] + root.border_width, \
                self.pos[1] + self.height/2 + self.border_width * 0.5 \
                ] if self.orientation == "vertical" else \
                [ \
                self.pos[0] + root.border_width, \
                self.pos[1] + root.border_width \
                ]
            size:
                [ \
                self.size[0] - root.border_width * 2, \
                self.size[1] / 2 - self.border_width * 1.5 \
                ] if self.orientation == "vertical" else \
                [ \
                self.size[0] / 2 - root.border_width * 1.5, \
                self.size[1] - root.border_width * 2 \
                ]
            radius:
                [root.border_radius, root.border_radius, 0, 0] \
                if self.orientation == "vertical" else \
                [root.border_radius, 0, 0, root.border_radius]

        #PM
        Color:
            rgba: root._pm_bg_color
        RoundedRectangle:
            pos:
                [ \
                self.pos[0] + root.border_width, \
                self.pos[1] + self.border_width \
                ] if self.orientation == "vertical" else \
                [ \
                self.pos[0] + root.size[0] / 2 + root.border_width / 2, \
                self.pos[1] + root.border_width \
                ]
            size:
                [ \
                self.size[0] - root.border_width * 2, \
                self.size[1] / 2 - self.border_width * 1.5 \
                ] if self.orientation == "vertical" else \
                [ \
                self.size[0] / 2 - root.border_width * 1.5, \
                self.size[1] - root.border_width * 2 \
                ]
            radius:
                [0, 0, root.border_radius, root.border_radius] \
                if self.orientation == "vertical" else \
                [0 ,root.border_radius, root.border_radius, 0]

    # AM
    AmPmSelectorLabel:
        text: "AM"
        on_release: root.selected = "am"
        text_color: root.text_color

    AmPmSelectorLabel:
        text: "PM"
        on_release: root.selected = "pm"
        text_color: root.text_color


<TimeInputTextField>
    size_hint: None, 1
    width: dp(96)
    mode: "fill"
    active_line: False
    font_size: dp(56)
    radius: [dp(10), ]
    fill_color_normal:
        root.parent.parent.parent.accent_color \
        if root.parent.parent.parent.accent_color else \
        ( \
        [*root.parent.bg_color_active[:3], 0.5] \
        if root.parent.state in ["hour", "minute"] else \
        [*root.bg_color[:3], 0.5] \
        )
    fill_color_focus:
        (1, 1, 1, 0.5) \
        if root.parent.parent.parent.primary_color else \
        self.theme_cls.bg_dark
    text_color_focus:
        root.parent.parent.parent.accent_color \
        if root.parent.parent.parent.accent_color else \
        self.theme_cls.primary_color


<TimeInput>
    size_hint: None, None
    _hour: hour
    _minute: minute

    TimeInputTextField:
        id: hour
        num_type: "hour"
        pos: 0, 0
        text_color: root.text_color
        disabled: root.disabled
        on_text: root.dispatch("on_time_input")
        radius: root.hour_radius
        on_select:
            root.dispatch("on_hour_select")
            root.state = "hour"

    MDLabel:
        text: ":"
        size_hint: None, None
        size: dp(24), dp(80)
        halign: "center"
        valign: "center"
        font_size: dp(50)
        pos: dp(96), 0
        theme_text_color: "Custom"
        text_color: root.text_color

    TimeInputTextField:
        id: minute
        num_type: "minute"
        pos: dp(120), 0
        text_color: root.text_color
        disabled: root.disabled
        on_text: root.dispatch("on_time_input")
        radius: root.minute_radius
        on_select:
            root.dispatch("on_minute_select")
            root.state = "minute"


<CircularSelector>
    circular_padding: dp(28)
    size_hint: None, None
    size: [dp(256), dp(256)]
    row_spacing: dp(40)

    canvas.before:
        PushMatrix
        Scale:
            origin: self.scale_origin
            x: root.scale
            y: root.scale
        Color:
            rgba: root.bg_color
        Ellipse:
            size: self.size
            pos: self.pos
        PushMatrix
        Scale:
            origin: self.center
            x: root.content_scale
            y: root.content_scale
        Color:
            rgb: root.selector_color
            a: 0 if self.selector_pos == [0, 0] else 1
        Ellipse:
            size: self.selector_size, self.selector_size
            pos:
                [self.selector_pos[0] -  self.selector_size / 2, \
                self.selector_pos[1] - self.selector_size / 2]
        Ellipse:
            size: dp(10), dp(10)
            pos: [self.center[0] - dp(5), self.center[1] - dp(5)]
        Line:
            points: [self.center, self.selector_pos]
            width: dp(1)
    canvas.after:
        PopMatrix
        PopMatrix


<SelectorLabel>
    halign: "center"
    valign: "center"
    adaptive_size: True
    theme_text_color: "Custom"


<MDTimePicker>
    auto_dismiss: True
    size_hint: None, None
    _time_input: _time_input
    _selector: _selector
    _am_pm_selector: _am_pm_selector
    _minute_label: _minute_label
    _hour_label: _hour_label

    MDRelativeLayout:
        canvas.before:
            Color:
                rgba:
                    root.primary_color \
                    if root.primary_color \
                    else root.theme_cls.bg_normal

            RoundedRectangle:
                size: self.size
                radius: root.radius

        MDLabel:
            id: label_title
            font_style: "Body2"
            bold: True
            theme_text_color: "Custom"
            size_hint_x: None
            width: root.width
            adaptive_height: True
            text: root.title
            font_name: root.font_name
            pos: (dp(24), root.height - self.height - dp(18))
            text_color:
                root.text_toolbar_color if root.text_toolbar_color \
                else root.theme_cls.text_color

        TimeInput:
            id: _time_input
            bg_color:
                root.accent_color if root.accent_color else \
                root.theme_cls.primary_light
            bg_color_active:
                root.selector_color if root.selector_color \
                else root.theme_cls.primary_color
            text_color:
                root.input_field_text_color if root.input_field_text_color else \
                root.theme_cls.text_color
            on_time_input: root._get_time_input(*self.get_time())
            on_hour_select: _selector.switch_mode("hour")
            on_minute_select: _selector.switch_mode("minute")
            minute_radius: root.minute_radius
            hour_radius: root.hour_radius

        TimeInputLabel:
            id: _hour_label
            text: "Hour"
            opacity: 0
            text_color:
                root.text_toolbar_color if root.text_toolbar_color else \
                root.theme_cls.secondary_text_color

        TimeInputLabel:
            id: _minute_label
            text: "Minute"
            opacity: 0
            text_color:
                root.text_toolbar_color if root.text_toolbar_color else \
                root.theme_cls.secondary_text_color

        AmPmSelector:
            id: _am_pm_selector
            owner: root
            border_color:
                root.accent_color if root.accent_color else \
                root.theme_cls.primary_color
            border_radius: root.am_pm_radius
            bg_color:
                root.primary_color if root.primary_color else \
                root.theme_cls.bg_normal
            border_width: root.am_pm_border_width
            bg_color_active:
                root.selector_color if root.selector_color else \
                root.theme_cls.primary_light
            text_color:
                root.input_field_text_color if root.input_field_text_color else \
                root.theme_cls.text_color
            on_selected: root._get_am_pm(self.selected)

        CircularSelector:
            id: _selector
            text_color:
                root.text_color if root.text_color else \
                root.theme_cls.text_color
            bg_color:
                root.accent_color if root.accent_color else \
                root.theme_cls.primary_light
            selector_color:
                root.primary_color if root.primary_color else \
                root.theme_cls.primary_color
            font_name: root.font_name
            on_selector_change: root._get_dial_time(_selector)

        MDIconButton:
            id: input_clock_switch
            icon: "keyboard"
            pos: dp(12), dp(8)
            theme_icon_color: "Custom"
            icon_size: "24dp"
            on_release: root._switch_input()
            icon_color:
                root.text_toolbar_color if root.text_toolbar_color else \
                root.theme_cls.secondary_text_color

        MDFlatButton:
            id: cancel_button
            text: "CANCEL"
            on_release: root.dispatch("on_cancel", None)
            theme_text_color: "Custom"
            pos: root.width - self.width - ok_button.width - dp(10), dp(10)
            font_name: root.font_name
            text_color:
                root.theme_cls.primary_color \
                if not root.text_button_color else root.text_button_color

        MDFlatButton:
            id: ok_button
            width: dp(32)
            pos: root.width - self.width, dp(10)
            text: "OK"
            theme_text_color: "Custom"
            font_name: root.font_name
            text_color:
                root.theme_cls.primary_color \
                if not root.text_button_color else root.text_button_color
            on_release: root.dispatch("on_save", root._get_data())
