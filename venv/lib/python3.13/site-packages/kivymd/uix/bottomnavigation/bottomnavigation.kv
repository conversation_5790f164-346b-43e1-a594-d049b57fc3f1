#:import STANDARD_INCREMENT kivymd.material_resources.STANDARD_INCREMENT


<MDBottomNavigation>
    orientation: "vertical"
    height:
        STANDARD_INCREMENT if app.theme_cls.material_style == "M2" else "80dp"

    ScreenManager:
        id: tab_manager
        transition: root.transition(duration=root.transition_duration)
        on_current:
            root.dispatch( \
            "on_switch_tabs", \
            root._get_switchig_tab(self.current), \
            self.current \
            )

    MDBottomNavigationBar:
        id: bottom_panel
        size_hint_y: None
        radius: root.radius
        height:
            STANDARD_INCREMENT \
            if app.theme_cls.material_style == "M2" else \
            "80dp"
        md_bg_color:
            root.theme_cls.bg_dark \
            if not root.panel_color \
            else root.panel_color

        MDBoxLayout:
            id: tab_bar
            pos_hint: {"center_x": .5, "center_y": .5}
            size_hint: None, None
            height:
                STANDARD_INCREMENT \
                if app.theme_cls.material_style == "M2" else \
                "80dp"


<MDBottomNavigationHeader>
    md_bg_color: root.panel_color
    on_press: self.tab.dispatch("on_tab_press")
    on_release: self.tab.dispatch("on_tab_release")
    on_touch_down: self.tab.dispatch("on_tab_touch_down", *args)
    on_touch_move: self.tab.dispatch("on_tab_touch_move", *args)
    on_touch_up: self.tab.dispatch("on_tab_touch_up", *args)
    width:
        root.panel.width / len(root.panel.ids.tab_manager.screens) \
        if len(root.panel.ids.tab_manager.screens) != 0 \
        else root.panel.width
    padding:
        0, "12dp", 0, "12dp" if app.theme_cls.material_style == "M2" else "16dp"

    RelativeLayout:
        id: item_container

        MDIcon:
            id: _label_icon
            icon: root.tab.icon
            height: self.height
            badge_icon: root.tab.badge_icon
            theme_text_color: "Custom"
            text_color: root._text_color_normal
            opposite_colors: root.opposite_colors
            pos: [self.pos[0], self.pos[1]]
            font_size: "24dp"
            y: item_container.height - self.height
            pos_hint:
                {"center_x": .5, "center_y": .5} \
                if not root.panel.use_text else \
                {"center_x": .5, "top": 1}
            on_icon:
                if self.icon not in md_icons.keys(): \
                self.size_hint = (None, None); \
                self.width = self.font_size; \
                self.height = self.font_size

            canvas.before:
                Color:
                    rgba:
                        ( \
                        ( \
                        app.theme_cls.disabled_hint_text_color \
                        if not root.selected_color_background else \
                        root.selected_color_background \
                        ) \
                        if root.active else \
                        (0, 0, 0, 0) \
                        ) \
                        if app.theme_cls.material_style == "M3" else \
                        (0, 0, 0, 0)
                RoundedRectangle:
                    radius: [16,]
                    size: root._selected_region_width, dp(32)
                    pos:
                        self.center_x - root._selected_region_width / 2, \
                        self.center_y - (dp(16))

        MDLabel:
            id: _label
            text: root.tab.text
            size_hint_x: None
            text_size: None, root.height
            adaptive_height: True
            theme_text_color: "Custom"
            text_color: root._text_color_normal
            opposite_colors: root.opposite_colors
            font_size: root._label_font_size
            pos_hint: {"center_x": .5}
            y: -dp(4) if app.theme_cls.material_style == "M2" else 0
            font_style:
                "Button" if app.theme_cls.material_style == "M2" else "Body2"


<MDTab>
    md_bg_color: root.theme_cls.bg_normal
