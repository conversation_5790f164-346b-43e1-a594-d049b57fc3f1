<MDTextField>
    input_filter: self.field_filter
    do_backspace: self.do_backspace

    canvas.before:
        Clear

        # "round" mode.
        Color:
            group: "round-color"
            rgba: self._fill_color if self.mode == "round" else (0, 0, 0, 0)
        Ellipse:
            angle_start: 180
            angle_end: 360
            pos: self.x - self.height / 2 + dp(18), self.y
            size: self.height, self.height
        Ellipse:
            angle_start: 360
            angle_end: 540
            pos: (self.width - dp(18)) + self.x - self.height / 2.0, self.y
            size: self.height, self.height
        Rectangle:
            pos: self.x + dp(14), self.y
            size: self.width - dp(28), self.height

        Color:
            rgba:
                ( \
                (self.line_color_focus if not self.error else self.error_color) \
                if self.focus else ( \
                self.theme_cls.disabled_hint_text_color \
                if not self.line_color_normal else \
                self.line_color_normal) \
                ) \
                if self.mode == "round" else \
                (0, 0, 0, 0)
        SmoothLine:
            width: dp(1)
            rounded_rectangle:
                self.x, \
                self.y, \
                self.width, \
                self.height, \
                self.height / 2

        # "fill" mode.
        Color:
            group: "fill-color"
            rgba: self._fill_color if self.mode == "fill" else (0, 0, 0, 0)
        RoundedRectangle:
            pos: self.x, self.y
            size: self.width, self.height
            radius: self.radius

        # Static underline texture.
        Color:
            group: "static-underline-color"
            rgba:
                (self._line_color_normal \
                if self.line_color_normal else self.theme_cls.divider_color) \
                if self.mode == "line" else (0, 0, 0, 0)
        Line:
            points: self.x, self.y + dp(16), self.x + self.width, self.y + dp(16)
            width: 1
            dash_length: dp(3)
            dash_offset: 2 if self.disabled else 0

        # Active underline (on focus) texture.
        Color:
            group: "active-underline-color"
            rgba:
                self._line_color_focus \
                if self.mode in ("line", "fill") and self.active_line \
                else (0, 0, 0, 0)
        Rectangle:
            size: self._underline_width, dp(1)
            pos:
                self.center_x - (self._underline_width / 2), \
                self.y + (dp(16) if self.mode != "fill" else 0)

        # Helper text texture.
        Color:
            group: "helper-text-color"
            rgba:
                self.theme_cls.disabled_hint_text_color \
                if self.disabled else \
                self._helper_text_color
        Rectangle:
            texture: self._helper_text_label.texture
            size: self._helper_text_label.texture_size
            pos:
                self.x + (dp(16) if self.mode == "fill" else \
                (0 if self.mode not in ("round", "rectangle") else dp(12))), \
                self.y + (dp(-18) if self.mode in ("fill", "rectangle", "round") else dp(-2))

        # Right/left icon texture.
        Color:
            group: "right-left-icons-color"
            rgba:
                self.theme_cls.disabled_hint_text_color \
                if self.disabled else \
                (self._icon_right_color if self.icon_right else self._icon_left_color)
        Rectangle:
            texture:
                self._icon_right_label.texture if self.icon_right else self._icon_left_label.texture
            size:
                (0, 0) if (not self.icon_right and not self.icon_left) else \
                (self._icon_right_label.texture_size if self.icon_right else self._icon_left_label.texture_size)
            pos:
                ( \
                (self.width + self.x - (0 if self.mode != "round" else dp(4))) - \
                (self._icon_right_label.texture_size[1]) - dp(8), \
                self.center[1] - self._icon_right_label.texture_size[1] / 2 + ((dp(8) \
                if self.mode != "round" else 0) if self.mode != "fill" else 0) \
                if self.mode != "rectangle" else \
                self.center[1] - self._icon_right_label.texture_size[1] / 2 - dp(4) \
                ) \
                if self.icon_right else \
                ( \
                self.x + ((dp((0 if self.mode != "round" else 12)) \
                if self.mode != "rectangle" else dp(12)) \
                if self.mode != "fill" else (dp(4) if not self.icon_left else dp(16))), \

                self.center[1] - self._icon_left_label.texture_size[1] / 2 + (((dp(4) \
                if self.mode != "round" else 0) if self.mode not in ("rectangle", "fill") \
                else dp(8)) if self.mode != "fill" else 0) \
                if self.mode != "rectangle" else \
                self.center[1] - self._icon_left_label.texture_size[1] / 2 - dp(4) \
                )

        # Max length texture.
        Color:
            group: "max-length-color"
            rgba:
                self.theme_cls.disabled_hint_text_color \
                if self.disabled else \
                self._max_length_text_color
        Rectangle:
            texture: self._max_length_label.texture
            size: self._max_length_label.texture_size
            pos:
                self.x + self.width - self._max_length_label.texture_size[0] - dp(12), \
                self.y - (dp(2) if self.mode == "line" else dp(18))

        # Cursor blink.
        Color:
            rgba:
                ( \
                (self.text_color_focus if not self.error else self.error_color) \
                if self.focus \
                else self._text_color_normal \
                ) \
                if self.focus and not self._cursor_blink \
                else \
                (0, 0, 0, 0)
        Rectangle:
            pos: (int(x) for x in self.cursor_pos)
            size: 1, -self.line_height

        # "rectangle" mode
        Color:
            group: "rectangle-color"
            rgba:
                ( \
                (self.line_color_focus if not self.error else self.error_color) \
                if self.focus else \
                self.line_color_normal \
                ) \
                if self.mode == "rectangle" else \
                (0, 0, 0, 0)
        SmoothLine:
            width: dp(1)
            rounded_rectangle:
                self.x, \
                self.y, \
                self.width, \
                self.height - self._hint_text_label.texture_size[1] // 2, \
                root.radius[0]

        # The background color line of the widget on which the text field
        # is placed (for background hint text texture).
        Color:
            rgba:
                ( \
                ( \
                self.parent.md_bg_color \
                if hasattr(self.parent, "md_bg_color") \
                and self.parent.md_bg_color != [1, 1, 1, 0] else \
                self.theme_cls.bg_normal \
                ) \
                if self.focus else \
                ( \
                (0, 0, 0, 0) if not self.text else \
                ( \
                self.parent.md_bg_color \
                if hasattr(self.parent, "md_bg_color") \
                and self.parent.md_bg_color != [1, 1, 1, 0] else \
                self.theme_cls.bg_normal \
                ) \
                ) \
                ) \
                if self.mode == "rectangle" else \
                (0, 0, 0, 0)
        SmoothLine:
            width: dp(2)
            points:
                self.x + dp(10), \
                self.top - self._hint_text_label.texture_size[1] // 2, \
                self.x + dp(16) + self._hint_text_label.texture_size[0], \
                self.top - self._hint_text_label.texture_size[1] // 2

        # Text color.
        Color:
            group: "text-color"
            rgba:
                self.theme_cls.disabled_hint_text_color if self.disabled else \
                ( \
                self.text_color_focus if self.focus else self._text_color_normal
                ) \
                if not self.error else self.error_color

    canvas.after:
        # Hint text texture.
        Color:
            group: "hint-text-color"
            rgba:
                self.theme_cls.disabled_hint_text_color \
                if self.disabled else \
                self._hint_text_color
        Rectangle:
            texture: self._hint_text_label.texture
            size: self._hint_text_label.texture_size
            pos:
                ( \
                self.x + ((dp(16) if not self.icon_left else dp(52)) \
                if self.mode == "fill" else ( \
                ((0 if self.mode != "round" else dp(12)) if self.mode != "rectangle" else dp(12)) \
                if not self.icon_left else \
                (dp(36 if self.mode != "round" else 42) if self.mode != "rectangle" else dp(42)))) \
                if not self.focus and not self.text else \
                self.x + ((dp(16) if self.mode != "round" else dp(36 if not self.icon_left else 42)) \
                if self.mode in ("fill", "rectangle", "round") and \
                self.icon_left else (0 if self.mode != "round" else dp(12))) + self._hint_x
                ), \

                self.y + self.height + (((dp(4) if self.mode != "round" else dp(10)) \
                if self.mode != "line" else \
                dp(-6)) if self.mode != "rectangle" else dp(-4)) - self._hint_y


    font_name: "Roboto" if not self.font_name else self.font_name
    foreground_color: self.theme_cls.text_color
    bold: False
    padding:
        ( \
        ((0 if self.mode != "round" else "12dp") \
        if self.mode != "rectangle" else "12dp") \
        if not self.icon_left else \
        (("36dp" if self.mode != "round" else "42dp") \
        if self.mode != "rectangle" else "42dp") \
        ) \
        if self.mode != "fill" else \
        ("16dp" if not self.icon_left else "52dp"), \

        "24dp" if self.mode != "round" else "8dp", \

        ((0 if self.mode != "round" and not self.icon_left else dp(12)) \
        if self.mode != "rectangle" else "12dp") \
        if self.mode != "fill" and not self.icon_right else \
        ( \
        "14dp" \
        if not self.icon_right else \
        self._icon_right_label.texture_size[1] + (dp(20) \
        if self.mode != "round" else dp(24))), \

        "8dp" if self.mode == "fill" else \
        (("22dp" if self.mode != "round" else "8dp") \
        if self.icon_left and self.mode != "rectangle" else \
        ("16dp" if self.mode in ("fill", "rectangle") else \
        "20dp" if self.mode != "round" else "8dp"))
    multiline: False
    size_hint_y: None
    height: self.minimum_height


<TextfieldLabel>
    size_hint_x: None
    width: self.texture_size[0]
    shorten: True
    shorten_from: "right"


<MDTextFieldRect>
    on_focus:
        self.anim_rect((self.x, self.y, self.right, self.y, self.right, \
        self.top, self.x, self.top, self.x, self.y), 1) if self.focus \
        else self.anim_rect((self.x - dp(60), self.y - dp(60), \
        self.right + dp(60), self.y - dp(60),
        self.right + dp(60), self.top + dp(60), \
        self.x - dp(60), self.top + dp(60), \
        self.x - dp(60), self.y - dp(60)), 0)

    canvas.after:
        Color:
            group: "color"
            rgba: self._primary_color
        Line:
            group: "rectangle"
            width: dp(1.5)
            points:
                (
                self.x - dp(60), self.y - dp(60),
                self.right + dp(60), self.y - dp(60),
                self.right + dp(60), self.top + dp(60),
                self.x - dp(60), self.top + dp(60),
                self.x - dp(60), self.y - dp(60)
                )
