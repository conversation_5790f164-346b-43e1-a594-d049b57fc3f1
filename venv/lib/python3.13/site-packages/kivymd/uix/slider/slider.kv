#:import Thumb kivymd.uix.selectioncontrol.Thumb
#:import get_color_from_hex kivy.utils.get_color_from_hex
#:import colors kivymd.color_definitions.colors


<HintBoxContainer@MDCard>


<MDSlider>
    canvas:
        Clear
        Color:
            rgba:
                ( \
                self.track_color_disabled \
                if self.track_color_disabled else \
                self.theme_cls.disabled_hint_text_color) \
                if self.disabled else \
                ( \
                ( \
                self.track_color_active \
                if self.track_color_active else \
                ( \
                get_color_from_hex(colors["Gray"]["400"]) \
                if app.theme_cls.theme_style == "Light" else \
                (1, 1, 1, .3) \
                ) \
                ) \
                if self.active else \
                ( \
                self.track_color_inactive \
                if self.track_color_inactive else \
                ( \
                self.theme_cls.disabled_hint_text_color \
                if app.theme_cls.theme_style == "Light" else \
                get_color_from_hex(colors["Gray"]["800"]) \
                ) \
                ) \
                )
        Rectangle:
            size:
                (self.width - self.padding * 2 - self._offset[0], dp(4)) if \
                self.orientation == "horizontal" \
                else (dp(4),self.height - self.padding*2 - self._offset[1])
            pos:
                (self.x + self.padding + self._offset[0], self.center_y - dp(4)) \
                if self.orientation == "horizontal" else \
                (self.center_x - dp(4), self.y + self.padding + self._offset[1])

        # If 0 draw circle
        Color:
            rgba:
                (0, 0, 0, 0) if not self._is_off else \
                ( \
                self.track_color_disabled \
                if self.disabled and self.track_color_disabled else \
                ( \
                self.theme_cls.disabled_hint_text_color \
                if app.theme_cls.theme_style == "Light" else \
                get_color_from_hex(colors["Gray"]["800"]) \
                ) \
                )
        Line:
            width: 2
            circle:
                (self.x + self.padding + dp(3), self.center_y - dp(2), 8 \
                if self.active else 6 ) if self.orientation == "horizontal" \
                else (self.center_x - dp(2), self.y + self.padding + dp(3), 8 \
                if self.active else 6)

        Color:
            rgba:
                (0, 0, 0, 0) if self._is_off \
                else \
                ( \
                ( \
                self.color if self.color else \
                app.theme_cls.primary_color \
                ) \
                if not self.disabled else \
                ( \
                self.track_color_disabled \
                if self.track_color_disabled else \
                ( \
                (0, 0, 0, .26) \
                if app.theme_cls.theme_style == "Light" else (1, 1, 1, .3) \
                ) \
                ) \
                )
        Rectangle:
            size:
                ((self.width - self.padding * 2) * self.value_normalized, sp(4)) \
                if root.orientation == "horizontal" else \
                (sp(4), (self.height - self.padding * 2) * self.value_normalized)
            pos:
                (self.x + self.padding, self.center_y - dp(4)) \
                if self.orientation == "horizontal" else \
                (self.center_x - dp(4), self.y + self.padding)

    Thumb:
        id: thumb
        size_hint: None, None
        size:
            (dp(12), dp(12)) if root.disabled else ((dp(24), dp(24)) \
            if root.active else \
            (dp(16), dp(16)))
        pos:
            (root.value_pos[0] - dp(8), root.center_y - thumb.height / 2 - dp(2)) \
            if root.orientation == "horizontal" \
            else (root.center_x - thumb.width / 2 - dp(2), \
            root.value_pos[1] - dp(8))
        md_bg_color:
            (0, 0, 0, 0) if root._is_off else \
            ( \
            ( \
            root.thumb_color_disabled \
            if root.thumb_color_disabled else \
            get_color_from_hex(colors["Gray"]["800"]) \
            ) \
            if root.disabled else \
            ( \
            (root.thumb_color_active \
            if root.thumb_color_active else \
            root.theme_cls.primary_color \
            ) \
            if root.active else \
            ( \
            root.thumb_color_inactive \
            if root.thumb_color_inactive else \
            root.theme_cls.primary_color \
            ) \
            ) \
            )
        elevation: 0 if root._is_off else (3 if root.active else 1)

    HintBoxContainer:
        id: hint_box
        size_hint: None, None
        md_bg_color: root.hint_bg_color if root.hint_bg_color else [0, 0, 0, 0]
        elevation: 1.5
        opacity: 1 if root.active else 0
        radius: root.hint_radius
        padding: "6dp", "6dp", "6dp", "8dp"
        shadow_color:
            ([0, 0, 0, 0.6] if root.hint_bg_color else [0, 0, 0, 0]) \
            if root.active else \
            [0, 0, 0, 0]
        size:
            lbl_value.width + self.padding[0] * 2, \
            lbl_value.height + self.padding[0]
        pos:
            (root.value_pos[0] - dp(9), root.center_y - hint_box.height / 2 + dp(30)) \
            if root.orientation == "horizontal" else \
            (root.center_x - hint_box.width / 2 + dp(30), root.value_pos[1] - dp(8))

        MDLabel:
            id: lbl_value
            font_style: "Caption"
            halign: "center"
            theme_text_color: "Custom"
            -text_size: None, None
            adaptive_size: True
            pos_hint: {"center_x": .5, "center_y": .5}
            text_color:
                app.theme_cls.primary_color \
                if not root.hint_text_color else root.hint_text_color
            text:
                str(root.value) \
                if isinstance(root.step, float) else str(int(root.value))
