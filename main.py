"""
Turn Me On - Android Version
A mobile game where you click lightbulbs to keep the lights on while avoiding gremlins.
Converted from pygame to Kivy for Android compatibility.
"""

from kivy.app import App
from kivy.uix.widget import Widget
from kivy.uix.label import Label
from kivy.uix.button import Button
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.floatlayout import FloatLayout
from kivy.graphics import Color, Ellipse, Rectangle, Line
from kivy.clock import Clock
from kivy.core.audio import SoundLoader
from kivy.core.window import Window
from kivy.vector import Vector
from kivy.animation import Animation
from kivy.properties import NumericProperty, BooleanProperty, StringProperty, ListProperty

import random
import math
import json
import os

# Import UI components
from ui import GameUI, GameOverPopup, DifficultySelectionPopup

# --- Constants ---
SCREEN_WIDTH = 480
SCREEN_HEIGHT = 800
FPS = 60

# Colors (RGBA format for Kivy)
WHITE = (1, 1, 1, 1)
BLACK = (0, 0, 0, 1)
RED = (1, 0, 0, 1)
GREEN = (0, 1, 0, 1)
BLUE = (0, 0, 1, 1)
YELLOW = (1, 1, 0, 1)
PURPLE = (0.5, 0, 0.5, 1)
ORANGE = (1, 0.65, 0, 1)
CYAN = (0, 1, 1, 1)
PINK = (1, 0.75, 0.8, 1)

# Game Settings
BRIGHTNESS_DECAY = 0.15  # Brightness points per second
BULB_ON_VALUE = 25       # Brightness points gained when a bulb is turned on


class GameSprite(Widget):
    """Base class for all game objects"""
    speed = NumericProperty(0)
    active = BooleanProperty(True)
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.speed = random.uniform(2, 6)
        
    def update(self, dt):
        """Update sprite position and state"""
        if self.active:
            self.y -= self.speed * dt * 60  # Convert to pixels per frame equivalent
            # Remove if off screen
            if self.y < -100:
                self.remove_from_parent()


class Lightbulb(GameSprite):
    """A lightbulb that falls and can be turned on by touching"""
    is_on = BooleanProperty(False)
    
    def __init__(self, game_instance, **kwargs):
        super().__init__(**kwargs)
        self.game_instance = game_instance
        self.size = (40, 60)
        self.x = random.uniform(0, SCREEN_WIDTH - self.width)
        self.y = random.uniform(SCREEN_HEIGHT, SCREEN_HEIGHT + 200)
        
        # Draw the lightbulb
        with self.canvas:
            Color(*WHITE)
            self.bulb_shape = Ellipse(pos=self.pos, size=self.size)
        
        self.bind(pos=self.update_graphics)
        
    def update_graphics(self, *args):
        """Update graphics when position changes"""
        self.bulb_shape.pos = self.pos
        
    def turn_on(self):
        """Turn the lightbulb on"""
        if not self.is_on:
            self.is_on = True
            self.game_instance.lit_bulb_count += 1
            self.game_instance.update_overcharge()
            
            # Change color to yellow when on
            with self.canvas:
                self.canvas.clear()
                Color(*YELLOW)
                self.bulb_shape = Ellipse(pos=self.pos, size=self.size)
            
            return True
        return False
    
    def on_break(self):
        """Called when the bulb hits the ground"""
        brightness_loss = 0
        if self.is_on:
            brightness_loss = BULB_ON_VALUE
            self.game_instance.lit_bulb_count = max(0, self.game_instance.lit_bulb_count - 1)
            self.game_instance.update_overcharge()
        return brightness_loss


class Star(GameSprite):
    """A star to be collected for points"""
    is_unstable = BooleanProperty(False)
    visible = BooleanProperty(True)
    
    def __init__(self, game_instance, is_unstable=False, **kwargs):
        super().__init__(**kwargs)
        self.game_instance = game_instance
        self.is_unstable = is_unstable
        self.size = (35, 35)
        self.x = random.uniform(0, SCREEN_WIDTH - self.width)
        self.y = random.uniform(SCREEN_HEIGHT, SCREEN_HEIGHT + 200)
        self.speed = random.uniform(1, 4)
        
        # Electromagnetic interference properties
        self.stability_timer = 300 if is_unstable else 0  # 5 seconds at 60 FPS
        self.flicker_timer = 0
        
        # Draw the star
        self.draw_star()
        self.bind(pos=self.update_graphics)
        
    def draw_star(self):
        """Draw a star shape"""
        with self.canvas:
            if self.is_unstable:
                Color(1, 0.4, 0.4, 1)  # Reddish tint for unstable stars
            else:
                Color(*BLUE)
            
            # Simple star representation as a circle for now
            self.star_shape = Ellipse(pos=self.pos, size=self.size)
    
    def update_graphics(self, *args):
        """Update graphics when position changes"""
        if hasattr(self, 'star_shape'):
            self.star_shape.pos = self.pos
    
    def update(self, dt):
        """Update star movement and handle instability"""
        if self.is_unstable:
            self.stability_timer -= dt * 60  # Convert to frame equivalent
            
            # Flicker effect as star becomes more unstable
            if self.stability_timer < 120:  # Last 2 seconds
                self.flicker_timer += dt * 60
                if int(self.flicker_timer) % 10 == 0:  # Flicker every 10 frames
                    self.visible = not self.visible
                    self.opacity = 1.0 if self.visible else 0.0
            
            # Star disappears when timer runs out
            if self.stability_timer <= 0:
                self.remove_from_parent()
                return
        
        super().update(dt)


class Gremlin(GameSprite):
    """An enemy that bounces around the screen"""
    speed_x = NumericProperty(0)
    speed_y = NumericProperty(0)
    frozen = BooleanProperty(False)
    
    def __init__(self, game_instance, **kwargs):
        super().__init__(**kwargs)
        self.game_instance = game_instance
        self.size = (50, 50)
        self.x = random.uniform(0, SCREEN_WIDTH - self.width)
        self.y = random.uniform(100, SCREEN_HEIGHT - 200)
        
        # Random speeds for bouncing movement
        self.speed_x = random.choice([-4, -3, -2, 2, 3, 4])
        self.speed_y = random.choice([-4, -3, -2, 2, 3, 4])
        self.frozen_timer = 0
        
        # Draw the gremlin
        with self.canvas:
            Color(*PURPLE)
            self.gremlin_shape = Ellipse(pos=self.pos, size=self.size)
        
        self.bind(pos=self.update_graphics)
    
    def update_graphics(self, *args):
        """Update graphics when position changes"""
        self.gremlin_shape.pos = self.pos
    
    def update(self, dt):
        """Move the gremlin and bounce off screen edges"""
        if self.frozen:
            self.frozen_timer -= dt * 60
            if self.frozen_timer <= 0:
                self.frozen = False
            return
        
        # Move the gremlin
        self.x += self.speed_x * dt * 60
        self.y += self.speed_y * dt * 60
        
        # Bounce off the sides of the screen
        if self.x < 0:
            self.x = 0
            self.speed_x *= -1
        if self.x > SCREEN_WIDTH - self.width:
            self.x = SCREEN_WIDTH - self.width
            self.speed_x *= -1
        
        # Bounce off the top and bottom of the screen
        if self.y < 0:
            self.y = 0
            self.speed_y *= -1
        if self.y > SCREEN_HEIGHT - self.height:
            self.y = SCREEN_HEIGHT - self.height
            self.speed_y *= -1
    
    def freeze(self, duration):
        """Freeze the gremlin for a specified duration in frames"""
        self.frozen = True
        self.frozen_timer = duration


class PowerUp(GameSprite):
    """Base class for power-ups that fall and can be collected"""
    power_type = StringProperty('')
    
    def __init__(self, power_type, **kwargs):
        super().__init__(**kwargs)
        self.power_type = power_type
        self.size = (30, 30)
        self.x = random.uniform(0, SCREEN_WIDTH - self.width)
        self.y = random.uniform(SCREEN_HEIGHT, SCREEN_HEIGHT + 200)
        self.speed = random.uniform(1, 3)
        
        # Draw power-up based on type
        self.draw_powerup()
        self.bind(pos=self.update_graphics)
    
    def draw_powerup(self):
        """Draw the power-up icon based on its type"""
        with self.canvas:
            if self.power_type == 'freeze':
                Color(*CYAN)
            elif self.power_type == 'shield':
                Color(*BLUE)
            elif self.power_type == 'boost':
                Color(*YELLOW)
            elif self.power_type == 'multi':
                Color(*GREEN)
            
            self.powerup_shape = Ellipse(pos=self.pos, size=self.size)
    
    def update_graphics(self, *args):
        """Update graphics when position changes"""
        self.powerup_shape.pos = self.pos


class GameWidget(FloatLayout):
    """Main game widget that contains all game objects"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.game_app = None  # Will be set by the app
        
    def on_touch_down(self, touch):
        """Handle touch events"""
        if self.game_app:
            return self.game_app.handle_touch(touch.pos)
        return super().on_touch_down(touch)


class TurnMeOnApp(App):
    """Main application class"""
    
    def build(self):
        """Build the application"""
        # Set window size for development
        Window.size = (SCREEN_WIDTH, SCREEN_HEIGHT)

        # Initialize game state
        self.init_game_state()

        # Create main layout
        self.root_layout = FloatLayout()

        # Create main game widget
        self.game_widget = GameWidget()
        self.game_widget.game_app = self

        # Create UI overlay
        self.ui = GameUI(self)

        # Add widgets to root layout
        self.root_layout.add_widget(self.game_widget)
        self.root_layout.add_widget(self.ui)

        # Load assets
        self.load_assets()

        # Show difficulty selection
        self.show_difficulty_selection()

        return self.root_layout

    def init_game_state(self):
        """Initialize game state variables"""
        self.score = 0
        self.brightness = 100.0
        self.game_time = 0
        self.difficulty_level = 'okay'  # Default difficulty
        self.max_gremlins = 1
        self.gremlin_spawn_interval = 4.0
        self.paused = False
        self.game_over = False
        self.combo_count = 0
        self.combo_timer = 0

        # Overcharge system
        self.overcharge_level = 0
        self.lit_bulb_count = 0

        # Power-up effects
        self.shield_active = False
        self.shield_timer = 0

        # High scores
        self.high_scores = self.load_high_scores()

        # Difficulty settings
        self.difficulty_settings = {
            'easy': {
                'initial_gremlins': 1,
                'max_gremlins': 4,
                'spawn_interval': 6.0,
                'min_spawn_interval': 3.0,
                'difficulty_increase_time': 15,
                'brightness_decay': 0.10
            },
            'okay': {
                'initial_gremlins': 1,
                'max_gremlins': 6,
                'spawn_interval': 4.0,
                'min_spawn_interval': 2.0,
                'difficulty_increase_time': 10,
                'brightness_decay': 0.15
            },
            'mad_man': {
                'initial_gremlins': 2,
                'max_gremlins': 12,
                'spawn_interval': 2.0,
                'min_spawn_interval': 0.8,
                'difficulty_increase_time': 5,
                'brightness_decay': 0.20
            }
        }

        # Game object lists
        self.lightbulbs = []
        self.stars = []
        self.gremlins = []
        self.powerups = []

    def load_assets(self):
        """Load game sounds"""
        self.sounds = {}
        asset_path = "assets"

        try:
            self.sounds['bulb_click'] = SoundLoader.load(os.path.join(asset_path, 'bulb_click.wav'))
            self.sounds['bulb_break'] = SoundLoader.load(os.path.join(asset_path, 'bulb_break.wav'))
            self.sounds['gremlin_hit'] = SoundLoader.load(os.path.join(asset_path, 'gremlin_hit.wav'))
            self.sounds['star_collect'] = SoundLoader.load(os.path.join(asset_path, 'star_collect.wav'))
        except Exception as e:
            print(f"Could not load sounds: {e}")
            # Create dummy sound objects
            self.sounds = {
                'bulb_click': None,
                'bulb_break': None,
                'gremlin_hit': None,
                'star_collect': None
            }

    def play_sound(self, sound_name):
        """Play a sound if it exists"""
        if sound_name in self.sounds and self.sounds[sound_name]:
            self.sounds[sound_name].play()

    def load_high_scores(self):
        """Load high scores from file or create default scores"""
        try:
            with open('high_scores.json', 'r') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            return {'easy': 0, 'okay': 0, 'mad_man': 0}

    def save_high_scores(self):
        """Save high scores to file"""
        try:
            with open('high_scores.json', 'w') as f:
                json.dump(self.high_scores, f)
        except Exception as e:
            print(f"Could not save high scores: {e}")

    def update_game(self, dt):
        """Main game update loop"""
        if self.paused or self.game_over:
            return

        self.game_time += dt

        # Update all game objects
        self.update_sprites(dt)

        # Update power-up timers
        self.update_powerup_timers(dt)

        # Update combo timer
        self.update_combo(dt)

        # Check for collisions
        self.check_collisions()

        # Update difficulty
        self.update_difficulty()

        # Brightness decay
        if not self.shield_active:
            decay_rate = self.difficulty_settings[self.difficulty_level]['brightness_decay']
            self.brightness -= decay_rate * dt * 60

        self.brightness = max(0, min(100, self.brightness))

        # Check for game over
        if self.brightness <= 0:
            self.game_over = True
            self.save_high_score()

    def update_sprites(self, dt):
        """Update all sprites"""
        # Update and remove off-screen lightbulbs
        for bulb in self.lightbulbs[:]:
            bulb.update(dt)
            if bulb.y < -100:
                brightness_loss = bulb.on_break()
                if brightness_loss > 0 and not self.shield_active:
                    self.brightness -= brightness_loss
                    self.play_sound('bulb_break')
                self.lightbulbs.remove(bulb)
                bulb.parent.remove_widget(bulb)

        # Update stars
        for star in self.stars[:]:
            star.update(dt)
            if star.y < -100:
                self.stars.remove(star)
                star.parent.remove_widget(star)

        # Update gremlins
        for gremlin in self.gremlins:
            gremlin.update(dt)

        # Update powerups
        for powerup in self.powerups[:]:
            powerup.update(dt)
            if powerup.y < -100:
                self.powerups.remove(powerup)
                powerup.parent.remove_widget(powerup)

    def handle_touch(self, pos):
        """Handle touch events on game objects"""
        x, y = pos

        # Check power-ups first
        for powerup in self.powerups[:]:
            if (powerup.x <= x <= powerup.x + powerup.width and
                powerup.y <= y <= powerup.y + powerup.height):
                self.activate_powerup(powerup.power_type)
                self.powerups.remove(powerup)
                powerup.parent.remove_widget(powerup)
                return True

        # Check gremlins
        for gremlin in self.gremlins[:]:
            if (gremlin.x <= x <= gremlin.x + gremlin.width and
                gremlin.y <= y <= gremlin.y + gremlin.height):
                self.play_sound('gremlin_hit')
                self.gremlins.remove(gremlin)
                gremlin.parent.remove_widget(gremlin)
                self.add_combo()
                return True

        # Check lightbulbs
        for bulb in self.lightbulbs:
            if (bulb.x <= x <= bulb.x + bulb.width and
                bulb.y <= y <= bulb.y + bulb.height):
                if bulb.turn_on():
                    self.play_sound('bulb_click')
                    self.brightness += BULB_ON_VALUE
                    self.add_combo()
                return True

        # Check stars
        for star in self.stars[:]:
            if (star.x <= x <= star.x + star.width and
                star.y <= y <= star.y + star.height and star.visible):
                # Calculate points
                base_points = 1
                overcharge_multiplier = self.get_overcharge_multiplier()
                combo_bonus = self.combo_count // 3
                total_points = int((base_points + combo_bonus) * overcharge_multiplier)
                self.score += total_points

                self.play_sound('star_collect')
                self.stars.remove(star)
                star.parent.remove_widget(star)
                self.add_combo()
                return True

        return False

    def spawn_lightbulb(self, dt):
        """Spawn a new lightbulb"""
        if not self.game_over and not self.paused:
            bulb = Lightbulb(self)
            self.lightbulbs.append(bulb)
            self.game_widget.add_widget(bulb)

    def spawn_star(self, dt):
        """Spawn a new star"""
        if not self.game_over and not self.paused:
            # Determine if star should be unstable
            is_unstable = self.is_overcharged() and random.random() < 0.7

            star = Star(self, is_unstable)
            self.stars.append(star)
            self.game_widget.add_widget(star)

            # Spawn additional stars based on overcharge level
            extra_stars = self.get_extra_star_count()
            for _ in range(extra_stars):
                unstable_chance = 0.8 if self.overcharge_level >= 3 else 0.6
                extra_unstable = self.is_overcharged() and random.random() < unstable_chance
                extra_star = Star(self, extra_unstable)
                self.stars.append(extra_star)
                self.game_widget.add_widget(extra_star)

    def spawn_gremlin(self, dt):
        """Spawn a new gremlin"""
        if not self.game_over and not self.paused and len(self.gremlins) < self.max_gremlins:
            gremlin = Gremlin(self)
            self.gremlins.append(gremlin)
            self.game_widget.add_widget(gremlin)

    def spawn_powerup(self, dt):
        """Spawn a random power-up"""
        if not self.game_over and not self.paused:
            power_types = ['freeze', 'shield', 'boost', 'multi']
            power_type = random.choice(power_types)
            powerup = PowerUp(power_type)
            self.powerups.append(powerup)
            self.game_widget.add_widget(powerup)

    def activate_powerup(self, power_type):
        """Activate a power-up effect"""
        if power_type == 'freeze':
            # Freeze all gremlins for 3 seconds
            for gremlin in self.gremlins:
                gremlin.freeze(180)  # 3 seconds at 60 FPS

        elif power_type == 'shield':
            # Activate shield for 5 seconds
            self.shield_active = True
            self.shield_timer = 300  # 5 seconds at 60 FPS

        elif power_type == 'boost':
            # Instant brightness boost
            self.brightness = min(100, self.brightness + 50)

        elif power_type == 'multi':
            # Turn on all lightbulbs currently on screen
            for bulb in self.lightbulbs:
                if bulb.turn_on():
                    self.brightness += BULB_ON_VALUE

    def update_powerup_timers(self, dt):
        """Update power-up effect timers"""
        if self.shield_active:
            self.shield_timer -= dt * 60
            if self.shield_timer <= 0:
                self.shield_active = False

    def add_combo(self):
        """Add to combo count and reset timer"""
        self.combo_count += 1
        self.combo_timer = 120  # 2 seconds at 60 FPS

    def update_combo(self, dt):
        """Update combo timer and reset if expired"""
        if self.combo_timer > 0:
            self.combo_timer -= dt * 60
        else:
            self.combo_count = 0

    def check_collisions(self):
        """Check for collisions between gremlins and other objects"""
        for gremlin in self.gremlins:
            # Check gremlin vs lightbulbs
            for bulb in self.lightbulbs[:]:
                if (abs(gremlin.x - bulb.x) < 40 and abs(gremlin.y - bulb.y) < 40):
                    self.play_sound('bulb_break')
                    if bulb.is_on:
                        self.brightness -= BULB_ON_VALUE
                        self.lit_bulb_count = max(0, self.lit_bulb_count - 1)
                        self.update_overcharge()
                    self.lightbulbs.remove(bulb)
                    bulb.parent.remove_widget(bulb)

            # Check gremlin vs stars
            for star in self.stars[:]:
                if (abs(gremlin.x - star.x) < 40 and abs(gremlin.y - star.y) < 40):
                    self.play_sound('star_collect')
                    self.stars.remove(star)
                    star.parent.remove_widget(star)

    def save_high_score(self):
        """Save high score if it's a new record"""
        if self.score > self.high_scores[self.difficulty_level]:
            self.high_scores[self.difficulty_level] = self.score
            self.save_high_scores()

    def update_overcharge(self):
        """Update overcharge level based on number of lit bulbs"""
        self.overcharge_level = max(0, self.lit_bulb_count - 2)

    def get_overcharge_multiplier(self):
        """Get point multiplier based on current overcharge level"""
        if self.overcharge_level >= 5:
            return 3.0
        elif self.overcharge_level >= 3:
            return 2.5
        elif self.overcharge_level >= 2:
            return 2.0
        elif self.overcharge_level >= 1:
            return 1.5
        else:
            return 1.0

    def get_extra_star_count(self):
        """Get number of extra stars to spawn based on overcharge level"""
        if self.overcharge_level >= 4:
            return 3
        elif self.overcharge_level >= 2:
            return 2
        elif self.overcharge_level >= 1:
            return 1
        else:
            return 0

    def is_overcharged(self):
        """Check if the system is overcharged"""
        return self.overcharge_level > 0

    def update_difficulty(self):
        """Increase difficulty over time"""
        settings = self.difficulty_settings[self.difficulty_level]
        difficulty_level = int(self.game_time // settings['difficulty_increase_time'])

        # Increase max gremlins
        new_max_gremlins = min(settings['initial_gremlins'] + difficulty_level, settings['max_gremlins'])
        if new_max_gremlins > self.max_gremlins:
            self.max_gremlins = new_max_gremlins

    def show_difficulty_selection(self):
        """Show difficulty selection popup"""
        popup = DifficultySelectionPopup(self)
        popup.open()

    def apply_difficulty_settings(self):
        """Apply the selected difficulty settings to the game"""
        settings = self.difficulty_settings[self.difficulty_level]
        self.max_gremlins = settings['initial_gremlins']
        self.gremlin_spawn_interval = settings['spawn_interval']

        # Spawn initial gremlins
        for _ in range(settings['initial_gremlins']):
            self.spawn_gremlin(0)  # Pass 0 as dt parameter

    def start_game(self):
        """Start the main game loop and spawning"""
        # Start game loop
        Clock.schedule_interval(self.update_game, 1.0 / FPS)

        # Start spawning timers
        Clock.schedule_interval(self.spawn_lightbulb, 1.0)
        Clock.schedule_interval(self.spawn_star, 2.5)
        Clock.schedule_interval(self.spawn_gremlin, self.gremlin_spawn_interval)
        Clock.schedule_interval(self.spawn_powerup, 15.0)

        # Update UI
        Clock.schedule_interval(self.update_ui, 1.0 / 30)  # Update UI at 30 FPS

    def update_ui(self, dt):
        """Update UI elements"""
        if hasattr(self, 'ui'):
            self.ui.update_display()

        # Check for game over
        if self.game_over and not hasattr(self, 'game_over_shown'):
            self.game_over_shown = True
            self.show_game_over()

    def show_game_over(self):
        """Show game over popup"""
        # Stop all scheduled events
        Clock.unschedule(self.update_game)
        Clock.unschedule(self.spawn_lightbulb)
        Clock.unschedule(self.spawn_star)
        Clock.unschedule(self.spawn_gremlin)
        Clock.unschedule(self.spawn_powerup)
        Clock.unschedule(self.update_ui)

        popup = GameOverPopup(self)
        popup.open()

    def restart_game(self):
        """Restart the game"""
        # Clear all game objects
        for bulb in self.lightbulbs[:]:
            bulb.parent.remove_widget(bulb)
        for star in self.stars[:]:
            star.parent.remove_widget(star)
        for gremlin in self.gremlins[:]:
            gremlin.parent.remove_widget(gremlin)
        for powerup in self.powerups[:]:
            powerup.parent.remove_widget(powerup)

        # Reset game state
        self.init_game_state()

        # Remove game over flag
        if hasattr(self, 'game_over_shown'):
            del self.game_over_shown

        # Show difficulty selection again
        self.show_difficulty_selection()


if __name__ == '__main__':
    TurnMeOnApp().run()
