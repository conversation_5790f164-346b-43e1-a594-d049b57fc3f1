# Turn Me On - Android Game

A mobile version of the "Turn Me On" game, converted from pygame to Kivy for Android compatibility.

## Game Description

Turn Me On is an action game where you must keep the lights on by clicking falling lightbulbs while avoiding mischievous gremlins. Collect stars for points and power-ups for special abilities!

### Gameplay Features

- **Lightbulbs**: Click falling lightbulbs to turn them on and maintain brightness
- **Stars**: Collect blue stars for points with combo multipliers
- **Gremlins**: Avoid purple gremlins that steal your lightbulbs and stars
- **Power-ups**: Collect special power-ups for temporary advantages:
  - 🧊 Freeze: Freezes all gremlins temporarily
  - 🛡️ Shield: Protects against brightness loss
  - ⚡ Boost: Instant brightness restoration
  - 🔄 Multi: Turns on all lightbulbs on screen
- **Overcharge System**: Keep multiple bulbs lit for point multipliers and extra stars
- **Difficulty Levels**: Easy, Okay, and Mad Man difficulties
- **Electromagnetic Interference**: Unstable stars that flicker and disappear

## Installation

### Prerequisites

- Python 3.8 or higher
- Java JDK 8 or higher (for Android building)
- Android SDK (automatically handled by buildozer)

### Quick Setup

1. Clone or download this project
2. Navigate to the project directory
3. Run the setup script:
   ```bash
   python setup.py
   ```
4. Choose option 6 for full setup

### Manual Setup

1. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

2. Initialize buildozer (for Android building):
   ```bash
   buildozer init
   ```

## Running the Game

### Desktop Testing
```bash
python main.py
```

### Building for Android

#### Debug APK (for testing)
```bash
buildozer android debug
```

#### Release APK (for distribution)
```bash
buildozer android release
```

The APK files will be created in the `bin/` directory.

## Project Structure

```
turnMeOnGameAndroid/
├── main.py              # Main game application
├── ui.py                # UI components and overlays
├── buildozer.spec       # Android build configuration
├── requirements.txt     # Python dependencies
├── setup.py            # Setup and build script
├── assets/             # Game assets (images and sounds)
│   ├── bulb_off.png
│   ├── bulb_on.png
│   ├── star.png
│   ├── gremlin.png
│   └── *.wav           # Sound files
└── README.md           # This file
```

## Controls

- **Touch/Click**: Interact with game objects
- **Pause Button**: Pause/resume the game (top-right corner)

## Game Mechanics

### Brightness System
- Start with 100% brightness
- Brightness decays over time
- Light bulbs restore brightness when clicked
- Game ends when brightness reaches 0%

### Scoring System
- Base points for collecting stars
- Combo multipliers for consecutive actions
- Overcharge multipliers based on lit bulbs:
  - 3+ bulbs: 1.5x points
  - 4 bulbs: 2.0x points
  - 5-6 bulbs: 2.5x points
  - 7+ bulbs: 3.0x points

### Difficulty Scaling
- More gremlins spawn over time
- Faster gremlin spawn rates
- Difficulty-specific settings for each level

## Development Notes

### Converting from Pygame to Kivy

This game was converted from a pygame-based desktop game to a Kivy-based mobile game. Key changes include:

1. **Event System**: Pygame events → Kivy touch events
2. **Graphics**: Pygame surfaces → Kivy canvas instructions
3. **Sprites**: Pygame sprites → Kivy widgets
4. **Audio**: pygame.mixer → Kivy SoundLoader
5. **Game Loop**: Pygame clock → Kivy Clock.schedule_interval

### Mobile Optimizations

- Touch-friendly UI elements
- Responsive layout for different screen sizes
- Optimized graphics rendering
- Battery-efficient update cycles

## Troubleshooting

### Common Issues

1. **Kivy not found**: Install kivy with `pip install kivy`
2. **Buildozer fails**: Ensure Java JDK is installed and JAVA_HOME is set
3. **Audio not working**: Check that audio files are in the assets/ directory
4. **APK won't install**: Enable "Unknown sources" in Android settings

### Build Issues

- Ensure all dependencies are installed
- Check that buildozer.spec has correct permissions
- Verify Android SDK is properly configured

## License

This project is open source. Feel free to modify and distribute.

## Credits

- Original pygame version converted to Kivy/Android
- Game assets included in the assets/ directory
- Built with Kivy framework for cross-platform compatibility
